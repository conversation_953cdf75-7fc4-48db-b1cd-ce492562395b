package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.*;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.EpsMapper;
import ma.almobadara.backend.mapper.ZoneMapper;
import ma.almobadara.backend.model.administration.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.repository.administration.*;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class ZoneService {

    private final ZoneRepository zoneRepository;
    private final HistoryZoneRepository historyZoneRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final AssistantRepository assistantRepository;
    private final ZoneMapper zoneMapper = ZoneMapper.INSTANCE;
    private final RefController refController;
    private final EntityManager entityManager;
    private final AssistantZoneRepository assistantZoneRepository;
    private final EpsRepository epsRepository;
    private final EpsMapper epsMapper;
    private final SousZoneRepository sousZoneRepository;
    private final FamilyRepository familyRepository;
    private final EpsService epsService;


    public Page<ZoneDTO> getAllZones(int page, int size, String searchByCode, String searchByAssistantName,
                                     String searchByName, String searchByNameAr, String searchByStatus, String searchByCity) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllZones with page: {}, size: {}, searchByCode: {}, searchByAssistantName: {}, searchByName: {}, searchByNameAr: {}, searchByStatus: {} , searchByCity: {}",
                page, size, searchByCode, searchByAssistantName, searchByName, searchByNameAr, searchByStatus, searchByCity);

        Pageable pageable = PageRequest.of(page, size);
        Page<Zone> zones;
        if (searchByCode != null || searchByAssistantName != null || searchByName != null ||
                searchByNameAr != null || searchByStatus != null || searchByCity != null) {
            zones = filterZones(searchByCode, searchByAssistantName, searchByName, searchByNameAr, searchByStatus, searchByCity, pageable);

        } else {
            zones = zoneRepository.findAllWithDeletedIsFalse(pageable);

        }

        List<ZoneDTO> zoneDTOs = zones.getContent().stream()
                .map(zoneMapper::zoneToZoneDTO)
                .collect(Collectors.toList());


        for (int i = 0; i < zoneDTOs.size(); i++) {
            ZoneDTO zoneDTO = zoneDTOs.get(i);
            Zone zone = zones.getContent().get(i);
            zoneDTO.setEpsId(zone.getEps() != null ? zone.getEps().getId() : null);
            zoneDTO.setEps(zone.getEps() != null ? zone.getEps() : null);
            if (zoneDTO.getEps()!=null){
                zoneDTO.getEps().setZone(null);
                zoneDTO.getEps().setServices(null);
            }
            if(zone.getAssistantZone()!=null){
                zoneDTO.setAssistantId(zone.getAssistantZone().getAssistant().getId());
                zoneDTO.setAssistantName(zone.getAssistantZone().getAssistant().getFirstName()+" "+zone.getAssistantZone().getAssistant().getLastName());
                zoneDTO.setAssistantEmail(zone.getAssistantZone().getAssistant().getEmail());
            }

         }

        // Populate detailed city information
        zoneDTOs.forEach(this::processCityDetails);

        log.debug("End service getAllZones with {} zones found, took {}", zones.getTotalElements(), watch.toMS());
        return new PageImpl<>(zoneDTOs, pageable, zones.getTotalElements());
    }

    private Page<Zone> filterZones(String searchByCode, String searchByAssistantName, String searchByName,
                                   String searchByNameAr, String searchByStatus, String searchByCity, Pageable pageable) {
        log.debug("Start service filterZones with searchByCode: {}, searchByAssistantName: {}, searchByName: {}, searchByNameAr: {}, searchByStatus: {}",
                searchByCode, searchByAssistantName, searchByName, searchByNameAr, searchByStatus);

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Zone> criteriaQuery = criteriaBuilder.createQuery(Zone.class);
        Root<Zone> root = criteriaQuery.from(Zone.class);

        Predicate predicate = buildPredicate(criteriaBuilder, root, searchByCode, searchByAssistantName, searchByName, searchByNameAr, searchByStatus, searchByCity);
        criteriaQuery.where(predicate);

        TypedQuery<Zone> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Zone> resultList = typedQuery.getResultList();

        log.debug("End service filterZones with {} zones found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<Zone> root,
                                     String searchByCode, String searchByAssistantName, String searchByName,
                                     String searchByNameAr, String searchByStatus, String searchByCity) {
        Predicate predicate = criteriaBuilder.conjunction();
        predicate = criteriaBuilder.and(predicate, criteriaBuilder.isFalse(root.get("isDeleted")));

        if (searchByCode != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("code")),
                    "%" + searchByCode.toLowerCase() + "%"));
        }

        if (searchByAssistantName != null) {
            // Join with Assistant and CacheAdUser to search by assistant name
            jakarta.persistence.criteria.Join<Zone, Assistant> assistantJoin = root.join("assistant", jakarta.persistence.criteria.JoinType.LEFT);
            jakarta.persistence.criteria.Join<Assistant, CacheAdUser> cacheAdUserJoin = assistantJoin.join("cacheAdUser", jakarta.persistence.criteria.JoinType.LEFT);

            // Create a predicate to search in both firstName and lastName of CacheAdUser
            Predicate firstNamePredicate = criteriaBuilder.like(
                criteriaBuilder.lower(cacheAdUserJoin.get("firstName")),
                "%" + searchByAssistantName.toLowerCase() + "%"
            );

            Predicate lastNamePredicate = criteriaBuilder.like(
                criteriaBuilder.lower(cacheAdUserJoin.get("lastName")),
                "%" + searchByAssistantName.toLowerCase() + "%"
            );

            // Combine the predicates with OR to match either firstName or lastName
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(firstNamePredicate, lastNamePredicate));
        }

        if (searchByName != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("name")),
                    "%" + searchByName.toLowerCase() + "%"));
        }

        if (searchByNameAr != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("nameAr")),
                    "%" + searchByNameAr.toLowerCase() + "%"));
        }

        if (searchByStatus != null) {
            // the actif  is just have active ( the zone that have a assistant)
            if (searchByStatus.equals("active")) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNotNull(root.get("assistant")));
            } else if (searchByStatus.equals("inactive")) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNull(root.get("assistant")));
            }
        }

        if (searchByCity != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("cityIds")),
                    "%" + searchByCity + "%"));
        }

        return predicate;
    }

    public List<ZoneDTO> getAllZonesList() {
        List<Zone> zones = zoneRepository.findAllByIsDeletedIsFalse();
        List<ZoneDTO> zoneDTOs = zones.stream()
                .map(zoneMapper::zoneToZoneDTO)
                .collect(Collectors.toList());

        List<ZoneDTO> zoneDTOSUpdated = new ArrayList<>();

        for (int i = 0; i < zoneDTOs.size(); i++) {
            ZoneDTO zoneDTO = zoneDTOs.get(i);
            Zone zone = zones.get(i);

            if (zone.getEps() != null) {
                zoneDTO.setEpsId(zone.getEps().getId());
                zoneDTO.setEps( zone.getEps());
                zoneDTO.getEps().setZone(null);
                zoneDTO.getEps().setServices(null);
            }

            zoneDTOSUpdated.add(zoneDTO);
        }


        // Populate detailed city information
        zoneDTOSUpdated.forEach(this::processCityDetails);

        return zoneDTOSUpdated;
    }

    private CityWithRegionAndCountryDTO fetchCityDetails(Long cityId) {
        // Implement the logic to fetch city details from the referential service
        return refController.getCityWithRegionAndCountry(cityId).getBody();
    }

    public ZoneDTO getZoneById(Long id) throws TechnicalException {
        // we should also get the city details
        Zone zone = zoneRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("Zone with ID " + id + " does not exist"));
        if (zone.getEps() != null) {
            zone.getEps().setZone(null);
        }
        ZoneDTO zoneDTO = zoneMapper.zoneToZoneDTO(zone);
         zoneDTO.setEps(zone.getEps() != null ? zone.getEps() : null);
        zoneDTO.setEpsId(zone.getEps() != null ? zone.getEps().getId() : null);
        if ( zoneDTO.getEps() !=null) {
            zoneDTO.getEps().setZone(null);
            zoneDTO.getEps().setServices(null);
        }
        if(zone.getAssistantZone()!=null){
            zoneDTO.setAssistantEmail(zone.getAssistantZone().getAssistant().getEmail());
            zoneDTO.setAssistantId(zone.getAssistantZone().getAssistant().getId());
            zoneDTO.setAssistantDateAffectationToZone(zone.getAssistantZone().getDateAffectation());
            zoneDTO.setAssistantDateEndAffectationToZone(zone.getAssistantZone().getDateEndAffectation());
            zoneDTO.setAssistantName(zone.getAssistantZone().getAssistant().getFirstName()+" "+zone.getAssistantZone().getAssistant().getLastName());
        }
        processCityDetails(zoneDTO);
        return zoneDTO;
    }

    //getZoneById
    Zone findZoneById(Long id) throws TechnicalException {
        return zoneRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("Zone with ID " + id + " does not exist"));
    }

    // we should create a fucntion for code generation like Z + Year + 001
    public String generateZoneCode() {
        // Fetch the latest zone code
        Zone lastZone = zoneRepository.findFirstByOrderByCodeDesc();
        String lastCode = (lastZone != null) ? lastZone.getCode() : null;

        // Get the current year
        String currentYear = String.valueOf(LocalDateTime.now().getYear());
        String newCode;

        if (lastCode != null && lastCode.substring(1, 5).equals(currentYear)) {
            // Extract and increment the last number
            int lastNumber = Integer.parseInt(lastCode.substring(5));
            newCode = String.format("Z%s%03d", currentYear, lastNumber + 1);
        } else {
            // Start with 001 if no code exists for the current year
            newCode = String.format("Z%s001", currentYear);
        }

        return newCode;
    }


    public ZoneDTO createZone(ZoneDTO zoneDTO) throws TechnicalException {
        // Check if a zone with the same name or code already exists
        Optional<Zone> existingZoneByName = zoneRepository.findByName(zoneDTO.getName().trim());
        Optional<Zone> existingZoneByCode = zoneRepository.findByCode(zoneDTO.getCode().trim());

        if (existingZoneByName.isPresent() && !existingZoneByName.get().isDeleted()) {
            log.debug("Zone with the same name already exists");
            throw new TechnicalException("Zone with the same name already exists");
        }
        if (existingZoneByCode.isPresent() && !existingZoneByCode.get().isDeleted()) {
            log.debug("Zone with the same code already exists");
            throw new TechnicalException("Zone with the same code already exists");
        }

        Zone zone = zoneMapper.zoneDTOToZone(zoneDTO);
        zone.setCityIdList(zoneDTO.getCityIds());

        if (zoneDTO.getEpsId() !=null) {
            Eps eps = epsRepository.findById(zoneDTO.getEpsId())
                    .orElseThrow(() -> new TechnicalException("EPS with ID " + zoneDTO.getEpsId() + " does not exist"));
            zone.setEps(eps);
            if (zone.getEps() != null) {
                zone.getEps().setZone(null);
            }
        }



        // Convert city IDs list to comma-separated string

        // Save the new zone
        Zone savedZone = zoneRepository.save(zone);

        Optional<Assistant> assistantOpt=assistantRepository.findById(zoneDTO.getAssistantId());
        if(assistantOpt.isPresent()){
            Assistant assistant=assistantOpt.get();
            AssistantZone assistantZone=AssistantZone.builder().zone(zone).assistant(assistant).dateAffectation(zoneDTO.getAssistantDateAffectationToZone()).dateEndAffectation(zoneDTO.getAssistantDateEndAffectationToZone()).build();
            assistantZoneRepository.save(assistantZone);
        }

        if (zone.getEps() != null) {
            Eps eps = epsRepository.findById(zone.getEps().getId())
                    .orElseThrow(() -> new TechnicalException("EPS with ID " + zone.getEps().getId() + " does not exist"));
            eps.setStatus(true);
            epsRepository.save(eps);
        }
        ZoneDTO zoneDTOResult = zoneMapper.zoneToZoneDTO(savedZone);
        zoneDTOResult.setEpsId(savedZone.getEps() != null ? savedZone.getEps().getId() : null);
        if (savedZone.getEps()!=null){ // if the eps is not null we should get the eps
            zoneDTOResult.setEps(savedZone.getEps());
        }

        // Return the saved zone as DTO
        return zoneDTOResult;
    }


    public ZoneDTO updateZone(Long id, ZoneDTO zoneDTO) throws TechnicalException {
        // Check if the zone with the given ID exists
        if (!zoneRepository.existsById(id)) {
            throw new TechnicalException("Zone with ID " + id + " does not exist");
        }

        // Retrieve the current zone for comparison
        Zone currentZone = zoneRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("Zone with ID " + id + " does not exist"));

        // Check if a zone with the same name or code already exists, excluding the current zone
        Optional<Zone> existingZoneByName = zoneRepository.findByName(zoneDTO.getName());
        Optional<Zone> existingZoneByCode = zoneRepository.findByCode(zoneDTO.getCode());

        if (existingZoneByName.isPresent() && !existingZoneByName.get().getId().equals(id)) {
            throw new TechnicalException("Zone with the same name already exists");
        }
        if (existingZoneByCode.isPresent() && !existingZoneByCode.get().getId().equals(id)) {
            throw new TechnicalException("Zone with the same code already exists");
        }

        // Retrieve the existing zone and update its attributes
        Zone zone = zoneRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("Zone with ID " + id + " does not exist"));
        zone.setName(zoneDTO.getName());
        zone.setNameAr(zoneDTO.getNameAr());
        zone.setDetails(zoneDTO.getDetails());
        zone.setCityIdList(zoneDTO.getCityIds()); // Update city IDs

        if (zone.getEps() != null) {
            zone.getEps().setZone(null);
        }

        // Save the updated zone
        Zone updatedZone = zoneRepository.save(zone);
        Optional<Assistant> assistantOpt=assistantRepository.findById(zoneDTO.getAssistantId());
        if(assistantOpt.isPresent()){
            Optional<AssistantZone> assistantZoneOpt= assistantZoneRepository.findByZone(zone);
            Assistant assistant=assistantOpt.get();

            AssistantZone assistantZone=AssistantZone.builder().zone(zone).assistant(assistant).dateAffectation(zoneDTO.getAssistantDateAffectationToZone()).dateEndAffectation(zoneDTO.getAssistantDateEndAffectationToZone()).build();

            if(assistantZoneOpt.isPresent()){
                assistantZone.setId(assistantZoneOpt.get().getId());
                HistoryZone historyZone=new HistoryZone();
                historyZone.setAssistant(assistantZoneOpt.get().getAssistant().getFirstName()+" "+assistantZoneOpt.get().getAssistant().getLastName());
                historyZone.setZone(zone);
                historyZone.setDateAffectation(assistantZoneOpt.get().getDateAffectation());
                historyZone.setDateFinAffectation(assistantZoneOpt.get().getDateEndAffectation());
                historyZoneRepository.save(historyZone);
            }
            assistantZoneRepository.save(assistantZone);
        }
        if (zone.getEps() != null) {
            Eps eps = epsRepository.findById(zone.getEps().getId())
                    .orElseThrow(() -> new TechnicalException("EPS with ID " + zone.getEps().getId() + " does not exist"));
            eps.setStatus(true);
            epsRepository.save(eps);
        }


        ZoneDTO zoneDTOResult = zoneMapper.zoneToZoneDTO(updatedZone);
        zoneDTOResult.setEpsId(updatedZone.getEps() != null ? updatedZone.getEps().getId() : null);
        if (updatedZone.getEps()!=null){ // if the eps is not null we should get the eps
            zoneDTOResult.setEps(updatedZone.getEps());
        }
        // Return the updated zone as DTO
        return zoneDTOResult;
    }


    public boolean deleteZone(Long id) throws TechnicalException {
        if (zoneRepository.existsById(id)) {
            // Soft delete the zone
            Zone zone = zoneRepository.getOne(id);
            // we should check if the zone have a assistant or  a beneficiary if yes we should not delete it and send a error message
            if (zone.getAssistantZone() != null) {
                log.debug("The zone is already assigned to an assistant");
                throw new TechnicalException("The zone is already assigned to an assistant");
            }

            if (!zone.getSousZones().isEmpty()) {
                sousZoneRepository.deleteAll(zone.getSousZones());
                zone.setSousZones(null);
            }

            if (historyZoneRepository.findByZoneId(id)!=null) {
                historyZoneRepository.deleteAll(historyZoneRepository.findByZoneId(id));
            }

            if (!zone.getBeneficiaries().isEmpty()) {
                log.debug("The zone is already assigned to a beneficiary");
                throw new TechnicalException("The zone is already assigned to a beneficiary");
            }
            if (familyRepository.findOneFamilyByZoneId(id) != null) {
                log.info("The zone is already assigned to a family");
                throw new TechnicalException("The zone is already assigned to a family");
            }

            if (zone.getEps() != null) {
                Eps eps = epsRepository.findById(zone.getEps().getId())
                        .orElseThrow(() -> new TechnicalException("EPS with ID " + zone.getEps().getId() + " does not exist"));
                eps.setStatus(false);
                epsRepository.save(eps);
            }
            zoneRepository.delete(zone);
            return true;
        }
        return false;
    }

    public List<CityWithRegionAndCountryDTO> getCityOfMorroco() {
        return refController.getCitiesByCountry(149L).getBody();

    }

    public void clearExpiredAssistants() {
        // Fetch all zones that have an assigned assistant
        List<Zone> zones = zoneRepository.findAll();

        // Current date for comparison
        LocalDate currentDate = LocalDate.now();

        for (Zone zone : zones) {
            // Check if the assistant's end date is not null and has passed
            if (zone.getAssistant() != null &&
                    zone.getAssistant().getDateEndAffectationToZone() != null &&
                    (zone.getAssistant().getDateEndAffectationToZone().isBefore(currentDate) || zone.getAssistant().getDateEndAffectationToZone().isEqual(currentDate))) {

                // Set the assistant field to null
                zone.setAssistant(null);

                // Save the updated zone
                zoneRepository.save(zone);
            }
        }
    }


    // get the disponible zones for the assistant = the zones that are not assigned to any assistant
    public List<ZoneDTO> getDisponibleZones() {
        // Clear expired assistants to update the zones
        clearExpiredAssistants();

        // Fetch zones that are active, not deleted, and without an assistant
        List<Zone> zones = zoneRepository.findAllByStatusIsTrueAndIsDeletedIsFalse()
                .stream()
                .filter(zone -> zone.getAssistant() == null)
                .toList();

        // Map zones to ZoneDTOs
        return zones.stream()
                .map(zoneMapper::zoneToZoneDTO)
                .collect(Collectors.toList());
    }


//    public ZoneDTO changeZoneStatus(Long zoneId, boolean newStatus) throws TechnicalException {
//        Zone zone = zoneRepository.findById(zoneId)
//                .orElseThrow(() -> new TechnicalException("Zone with ID " + zoneId + " does not exist"));
//
//        zone.setStatus(newStatus);
//        zone.setUpdatedAt(LocalDateTime.now());  // Met à jour le champ `updatedAt`
//        Zone updatedZone = zoneRepository.save(zone);
//
//        return zoneMapper.zoneToZoneDTO(updatedZone);
//    }

    public ZoneDTO changeZoneStatus(Long zoneId, boolean newStatus) throws TechnicalException {
        // Récupère la zone par son ID
        Zone zone = zoneRepository.findById(zoneId)
                .orElseThrow(() -> new TechnicalException("Zone with ID " + zoneId + " does not exist"));

        // Vérifie si la zone est assignée à un assistant
        if (zone.getAssistant() != null) {
            throw new TechnicalException("The zone is already assigned to an assistant");
        }

        // Met à jour le statut de la zone
        zone.setStatus(newStatus);
        zone.setUpdatedAt(LocalDateTime.now());  // Met à jour le champ `updatedAt`
        Zone updatedZone = zoneRepository.save(zone);

        // Retourne la zone mise à jour en tant que DTO
        return zoneMapper.zoneToZoneDTO(updatedZone);
    }


//    public ZoneDTO getZoneWithBeneficiariesAndAssistant(Long zoneId) {
//        // Récupérer la zone par ID
//        Zone zone = zoneRepository.findById(zoneId)
//                .orElseThrow(() -> new RuntimeException("Zone non trouvée"));
//
//        // Mapper la zone en DTO
//        ZoneDTO zoneDTO = zoneMapper.zoneToZoneDTO(zone);
//
//        // Ajouter l'assistant
//        if (zone.getAssistant() != null) {
//            AssistantDTO assistantDTO = zoneMapper.toAssistantDto(zone.getAssistant());
//            zoneDTO.setAssistantName(assistantDTO.getFirstName() + " " + assistantDTO.getLastName());
//            zoneDTO.setAssistantId(assistantDTO.getId());
//            zoneDTO.setAssistantCode(assistantDTO.getCode());
//            zoneDTO.setAssistantEmail(assistantDTO.getEmail());
//            zoneDTO.setAssistantDateAffectationToZone(assistantDTO.getDateAffectationToZone());
//        }
//
//        if (zone.getBeneficiaries() != null && !zone.getBeneficiaries().isEmpty()) {
//            List<ZoneDetailsDTO> beneficiaryDTOs = zone.getBeneficiaries().stream()
//                    .map(beneficiary -> zoneMapper.toBeneficiaryDto(beneficiary))
//                    .toList();
//            zoneDTO.setBeneficiaries(beneficiaryDTOs);
//            zoneDTO.setBeneficiariesCount(beneficiaryDTOs.size());
//        } else {
//            zoneDTO.setBeneficiaries(Collections.emptyList());
//            zoneDTO.setBeneficiariesCount(0);
//        }
//
//        processCityDetails(zoneDTO);
//
//        return zoneDTO;
//    }

//    public ZoneDTO getZoneWithBeneficiariesAndAssistants(Long zoneId) {
//        // Récupérer la zone par ID
//        Zone zone = zoneRepository.findById(zoneId)
//                .orElseThrow(() -> new RuntimeException("Zone non trouvée"));
//
//        // Mapper la zone en DTO
//        ZoneDTO zoneDTO = zoneMapper.zoneToZoneDTO(zone);
//
//        // Ajouter les assistants actuels et historiques
//        List<AssistantDTO> assistantDTOs = new ArrayList<>();
//
//        // Ajouter l'assistant actuel
//        if (zone.getAssistant() != null) {
//            AssistantDTO assistantDTO = zoneMapper.toAssistantDto(zone.getAssistant());
//            assistantDTOs.add(assistantDTO);
//        }
//
//        // Ajouter les assistants de l'historique
//        List<HistoryZone> historiqueZones = historyZoneRepository.findByZoneId(zoneId);
//        for (HistoryZone historique : historiqueZones) {
//            AssistantDTO assistantDTO = zoneMapper.toAssistantDto(historique.getAssistant());
//            assistantDTO.setDateAffectationToZone(historique.getDateAffectation());
//            assistantDTO.setDateEndAffectationToZone(historique.getDateFinAffectation());
//            assistantDTOs.add(assistantDTO);
//        }
//
//        // Set assistants in zoneDTO
//        zoneDTO.setAssistants(assistantDTOs);
//
//        // Ajouter les bénéficiaires
//        if (zone.getBeneficiaries() != null && !zone.getBeneficiaries().isEmpty()) {
//            List<ZoneDetailsDTO> beneficiaryDTOs = zone.getBeneficiaries().stream()
//                    .map(beneficiary -> zoneMapper.toBeneficiaryDto(beneficiary))
//                    .toList();
//            zoneDTO.setBeneficiaries(beneficiaryDTOs);
//            zoneDTO.setBeneficiariesCount(beneficiaryDTOs.size());
//        } else {
//            zoneDTO.setBeneficiaries(Collections.emptyList());
//            zoneDTO.setBeneficiariesCount(0);
//        }
//
//        processCityDetails(zoneDTO);
//
//        return zoneDTO;
//    }

//    public ZoneDTO getZoneWithBeneficiariesAndAssistants(Long zoneId, int assistantsPage, int assistantsPageSize, int beneficiariesPage, int beneficiariesPageSize) {
//        // Récupérer la zone par ID
//        Zone zone = zoneRepository.findById(zoneId)
//                .orElseThrow(() -> new RuntimeException("Zone non trouvée"));
//
//        // Mapper la zone en DTO
//        ZoneDTO zoneDTO = zoneMapper.zoneToZoneDTO(zone);
//
//        // Pagination pour les assistants actuels et historiques
//        PageRequest assistantPageRequest = PageRequest.of(assistantsPage, assistantsPageSize);
//        Page<Assistant> assistantPage = assistantRepository.findByZoneId(zoneId, assistantPageRequest);
//        List<AssistantDTO> assistantDTOs = assistantPage.getContent().stream()
//                .map(assistant -> zoneMapper.toAssistantDto(assistant))
//                .collect(Collectors.toList());
//
//        zoneDTO.setAssistants(assistantDTOs);
//        zoneDTO.setTotalAssistants((int) assistantPage.getTotalElements());
//        zoneDTO.setAssistantsPage(assistantsPage);
//        zoneDTO.setAssistantsPageSize(assistantsPageSize);
//
//
//        PageRequest beneficiaryPageRequest = PageRequest.of(beneficiariesPage, beneficiariesPageSize);
//        Page<Beneficiary> beneficiaryPage = beneficiaryRepository.findByZoneId(zoneId, beneficiaryPageRequest);
//        List<ZoneDetailsDTO> beneficiaryDTOs = beneficiaryPage.getContent().stream()
//                .map(beneficiary -> zoneMapper.toBeneficiaryDto(beneficiary))
//                .collect(Collectors.toList());
//
//        zoneDTO.setBeneficiaries(beneficiaryDTOs);
//        zoneDTO.setTotalBeneficiaries((int) beneficiaryPage.getTotalElements());
//        zoneDTO.setBeneficiariesPage(beneficiariesPage);
//        zoneDTO.setBeneficiariesPageSize(beneficiariesPageSize);
//
//        return zoneDTO;
//    }


//    public ZoneDTO getZoneWithBeneficiariesAndAssistants(Long zoneId, int assistantsPage, int assistantsPageSize, int beneficiariesPage, int beneficiariesPageSize) {
//        // Récupérer la zone par ID
//        Zone zone = zoneRepository.findById(zoneId)
//                .orElseThrow(() -> new RuntimeException("Zone non trouvée"));
//
//        // Mapper la zone en DTO
//        ZoneDTO zoneDTO = zoneMapper.zoneToZoneDTO(zone);
//
//        // Pagination pour les assistants actuels
//        PageRequest assistantPageRequest = PageRequest.of(assistantsPage, assistantsPageSize);
//        Page<Assistant> assistantPage = assistantRepository.findByZoneId(zoneId, assistantPageRequest);
//        List<AssistantDTO> assistantDTOs = assistantPage.getContent().stream()
//                .map(assistant -> zoneMapper.toAssistantDto(assistant))
//                .collect(Collectors.toList());
//
//        // Ajouter les assistants historiques
//        List<HistoryZone> historiqueZones = historyZoneRepository.findByZoneId(zoneId);
//        for (HistoryZone historique : historiqueZones) {
//            AssistantDTO assistantDTO = zoneMapper.toAssistantDto(historique.getAssistant());
//            assistantDTO.setDateAffectationToZone(historique.getDateAffectation());
//            assistantDTO.setDateEndAffectationToZone(historique.getDateFinAffectation());
//            assistantDTOs.add(assistantDTO);
//        }
//
//        zoneDTO.setAssistants(assistantDTOs);
//        zoneDTO.setTotalAssistants((int) assistantPage.getTotalElements() + historiqueZones.size());
//        zoneDTO.setAssistantsPage(assistantsPage);
//        zoneDTO.setAssistantsPageSize(assistantsPageSize);
//
//        // Pagination pour les bénéficiaires
//        PageRequest beneficiaryPageRequest = PageRequest.of(beneficiariesPage, beneficiariesPageSize);
//        Page<Beneficiary> beneficiaryPage = beneficiaryRepository.findByZoneId(zoneId, beneficiaryPageRequest);
//        List<ZoneDetailsDTO> beneficiaryDTOs = beneficiaryPage.getContent().stream()
//                .map(beneficiary -> zoneMapper.toBeneficiaryDto(beneficiary))
//                .collect(Collectors.toList());
//
//        zoneDTO.setBeneficiaries(beneficiaryDTOs);
//        zoneDTO.setTotalBeneficiaries((int) beneficiaryPage.getTotalElements());
//        zoneDTO.setBeneficiariesPage(beneficiariesPage);
//        zoneDTO.setBeneficiariesPageSize(beneficiariesPageSize);
//
//        return zoneDTO;
//    }


    public ZoneDTO getZoneWithBeneficiariesAndAssistants(Long zoneId, int assistantsPage, int assistantsPageSize, int beneficiariesPage, int beneficiariesPageSize) throws TechnicalException {
        // Retrieve the zone by ID
        Zone zone = zoneRepository.findById(zoneId)
                .orElseThrow(() -> new RuntimeException("Zone not found"));

        if (zone.getEps() != null) {
            zone.getEps().setZone(null);
        }
        // Map the zone to a DTO
        ZoneDTO zoneDTO = zoneMapper.zoneToZoneDTO(zone);

        zoneDTO.setEps(zone.getEps()!=null?zone.getEps():null);

        processCityDetails(zoneDTO);
        if (zone.getEps() != null) {
        zoneDTO.setEpsId(zone.getEps().getId());
        }
        // Handle current assistant details
        if (zone.getAssistant() != null) {
            AssistantDTO assistantDTO = zoneMapper.toAssistantDto(zone.getAssistant());
            // take the name of the  assiastn from the cacheAdUser
            if(zone.getAssistant().getCacheAdUser() != null){
                zoneDTO.setAssistantName(zone.getAssistant().getCacheAdUser().getFirstName() + " " + zone.getAssistant().getCacheAdUser().getLastName());
            }
            zoneDTO.setAssistantId(assistantDTO.getId());
            zoneDTO.setAssistantCode(assistantDTO.getCode());
            zoneDTO.setAssistantEmail(assistantDTO.getEmail());
            zoneDTO.setAssistantDateAffectationToZone(assistantDTO.getDateAffectationToZone());
        }
        if(zone.getAssistantZone()!=null){
            zoneDTO.setAssistantId(zone.getAssistantZone().getAssistant().getId());
            zoneDTO.setAssistantName(zone.getAssistantZone().getAssistant().getFirstName()+" "+zone.getAssistantZone().getAssistant().getLastName());
            zoneDTO.setAssistantEmail(zone.getAssistantZone().getAssistant().getEmail());
            zoneDTO.setAssistantDateAffectationToZone(zone.getAssistantZone().getDateAffectation());
            zoneDTO.setAssistantDateEndAffectationToZone(zone.getAssistantZone().getDateEndAffectation());
        }

        // Pagination for current assistants
        PageRequest assistantPageRequest = PageRequest.of(assistantsPage, assistantsPageSize);
        Page<Assistant> assistantPage = assistantRepository.findByZoneId(zoneId, assistantPageRequest);
        // Retrieve and map historical assistant data
        List<HistoryZone> historiqueZones = historyZoneRepository.findByZoneId(zoneId);
        List<HistoryZoneDTO> listHistoriqueZoneDTO = historiqueZones.stream()
                .map(zoneMapper::historyZoneToHistoryZoneDTO)
                .toList();
        zoneDTO.setHistoryZones(listHistoriqueZoneDTO);
        zoneDTO.setTotalAssistants((int) assistantPage.getTotalElements() + listHistoriqueZoneDTO.size());
        zoneDTO.setAssistantsPage(assistantsPage);
        zoneDTO.setAssistantsPageSize(assistantsPageSize);

        // Pagination for beneficiaries
        PageRequest beneficiaryPageRequest = PageRequest.of(beneficiariesPage, beneficiariesPageSize);
        Page<Beneficiary> beneficiaryPage = beneficiaryRepository.findByZoneId(zoneId, beneficiaryPageRequest);
        List<ZoneDetailsDTO> beneficiaryDTOs = beneficiaryPage.getContent().stream()
                .map(zoneMapper::toBeneficiaryDto)
                .collect(Collectors.toList());

        zoneDTO.setBeneficiaries(beneficiaryDTOs);
        zoneDTO.setTotalBeneficiaries((int) beneficiaryPage.getTotalElements());
        zoneDTO.setBeneficiariesPage(beneficiariesPage);
        zoneDTO.setBeneficiariesPageSize(beneficiariesPageSize);
        if ( zoneDTO.getEps() !=null) {
            zoneDTO.getEps().setServices(null);
        }

        return zoneDTO;
    }



    private void processCityDetails(ZoneDTO zoneDTO) {
        List<Long> cityIds = zoneDTO.getCityIds();
        if (cityIds != null && !cityIds.isEmpty()) {
            List<CityWithRegionAndCountryDTO> cityDetails = cityIds.stream()
                    .map(this::fetchCityDetails)
                    .collect(Collectors.toList());
            zoneDTO.setCityDetails(cityDetails); // Assuming you have a method to set detailed city info
        }
    }

}